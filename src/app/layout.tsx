import type { Metadata } from "next";
import { Geist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "LoveBook - Создайте персональную книгу полную любви",
  description: "Превратите ваши самые дорогие воспоминания в настоящую книгу. Создайте книгу-опросник или книгу-историю с фотографиями и аудио. Персональный подарок, который будет храниться вечно.",
  keywords: "персональная книга, подарок, воспоминания, семья, любовь, история, опросник",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ru">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
