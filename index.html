<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lovebook - Создайте книгу любви</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            padding: 2rem 0;
            text-align: center;
        }

        .logo {
            font-size: 3rem;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .tagline {
            font-size: 1.2rem;
            color: #7f8c8d;
            font-style: italic;
        }

        .hero {
            text-align: center;
            padding: 4rem 0;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            margin: 2rem 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            color: #7f8c8d;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .services {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 3rem;
            margin: 4rem 0;
        }

        .service-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 2.5rem;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 3px solid transparent;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
            border-color: #e74c3c;
        }

        .service-card.quiz {
            border-left: 5px solid #3498db;
        }

        .service-card.story {
            border-left: 5px solid #e74c3c;
        }

        .service-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .quiz .service-icon {
            color: #3498db;
        }

        .story .service-icon {
            color: #e74c3c;
        }

        .service-card h3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .service-card p {
            font-size: 1.1rem;
            color: #7f8c8d;
            margin-bottom: 1.5rem;
        }

        .features {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .features li {
            padding: 0.5rem 0;
            color: #34495e;
            position: relative;
            padding-left: 1.5rem;
        }

        .features li:before {
            content: "♥";
            color: #e74c3c;
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .cta-section {
            text-align: center;
            padding: 4rem 0;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            margin: 3rem 0;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 1.2rem 3rem;
            font-size: 1.3rem;
            text-decoration: none;
            border-radius: 50px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
            background: linear-gradient(45deg, #c0392b, #a93226);
        }

        footer {
            text-align: center;
            padding: 2rem 0;
            color: #7f8c8d;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .services {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .logo {
                font-size: 2.5rem;
            }
            
            .service-card {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">Lovebook</div>
            <div class="tagline">Создавайте книги с любовью</div>
        </header>

        <section class="hero">
            <h1>Превратите ваши чувства в настоящую книгу</h1>
            <p>Создайте уникальную книгу, наполненную теплом и любовью. Два способа рассказать свою историю — выберите тот, который ближе вашему сердцу.</p>
        </section>

        <section class="services">
            <div class="service-card quiz">
                <span class="service-icon">📝</span>
                <h3>Книга-викторина</h3>
                <p>Ответьте на готовые вопросы о ваших близких и создайте персональную книгу воспоминаний.</p>
                <ul class="features">
                    <li>Готовые шаблоны для мамы, папы, друзей</li>
                    <li>Вопросы о самых теплых воспоминаниях</li>
                    <li>Возможность добавить свои вопросы</li>
                    <li>Настройка стиля, шрифтов и обложки</li>
                    <li>Персональные ответы на каждый вопрос</li>
                </ul>
            </div>

            <div class="service-card story">
                <span class="service-icon">📖</span>
                <h3>Книга-история</h3>
                <p>Создайте собственную историю с текстом, изображениями и даже аудиозаписями.</p>
                <ul class="features">
                    <li>Свободное написание текста</li>
                    <li>Добавление изображений</li>
                    <li>Загрузка аудиофайлов с автоматической расшифровкой</li>
                    <li>Настройка дизайна и типографики</li>
                    <li>Полная творческая свобода</li>
                </ul>
            </div>
        </section>

        <section class="cta-section">
            <h2 style="margin-bottom: 1rem; color: #2c3e50;">Готовы создать свою книгу любви?</h2>
            <p style="margin-bottom: 2rem; color: #7f8c8d; font-size: 1.1rem;">Начните прямо сейчас и подарите близким незабываемые эмоции</p>
            <a href="/dashboard" class="cta-button">Создать книгу</a>
        </section>

        <footer>
            <p>Каждая книга — это история любви, рассказанная от сердца к сердцу ♥</p>
        </footer>
    </div>
</body>
</html>
